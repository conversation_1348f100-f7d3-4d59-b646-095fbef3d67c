-   tìm hiểu về Jito Shredstream tại tài liệu `https://docs.jito.wtf/lowlatencytxnfeed/`
-   tìm hiểu thêm các thông tin khác về Jito Shredstream trên internet
-   xem các file proto của jito shredstream: https://raw.githubusercontent.com/jito-labs/mev-protos/c9614089ef48fb83f01767d87e8f73e6c2e59c0b/shared.proto, https://raw.githubusercontent.com/jito-labs/mev-protos/c9614089ef48fb83f01767d87e8f73e6c2e59c0b/shredstream.proto
-   ví dụ về việc lắng nghe và giải mã shred: https://raw.githubusercontent.com/jito-labs/shredstream-proxy/refs/heads/master/examples/deshred.rs
-   struct Entry: https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html
-   từ các thông tin trên, hãy gi<PERSON><PERSON> tôi tạo 1 thư viện cho nodejs/typescript, sừ dụng wasm và dùng crate solana-entry và bincode để giải mã các `entries` trong message nhận được từ hàm `SubscribeEntries` khi subscribe lên jito shredstream server
-   chúng ta sẽ bắt đầu bằng việc lên kế hoạch trước, bạn hãy tạo cho tôi 1 file `README.md` đơn giản tại thư mục gốc của dự án này, file này sẽ chứa mô tả của thư viện, tính năng hỗ trợ, các liên kết tham khảo, cấu trúc cơ bản của dự án, kế hoạch phát triển theo từng bước, file này phải chứa đầy đủ context để các công cụ AI coding assistant có thể đọc và hiểu được rõ và chính xác mục đích của dự án
