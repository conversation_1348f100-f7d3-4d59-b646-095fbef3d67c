-   tìm hiểu về Jito Shredstream tại tài liệu `https://docs.jito.wtf/lowlatencytxnfeed/`
-   tìm hiểu thêm các thông tin khác về Jito Shredstream trên internet
-   xem các file proto của jito shredstream: https://raw.githubusercontent.com/jito-labs/mev-protos/c9614089ef48fb83f01767d87e8f73e6c2e59c0b/shared.proto, https://raw.githubusercontent.com/jito-labs/mev-protos/c9614089ef48fb83f01767d87e8f73e6c2e59c0b/shredstream.proto
-   ví dụ về việc lắng nghe và giải mã shred: https://raw.githubusercontent.com/jito-labs/shredstream-proxy/refs/heads/master/examples/deshred.rs
-   struct Entry: https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html
-   từ các thông tin trên, hãy gi<PERSON><PERSON> tôi tạo 1 thư viện cho nodejs/typescript, sừ dụng wasm và dùng crate solana-entry và bincode để giải mã các `entries` trong message nhận được từ hàm `SubscribeEntries` khi subscribe lên jito shredstream server
-   chúng ta sẽ bắt đầu bằng việc lên kế hoạch trước, bạn hãy tạo cho tôi 1 file `README.md` đơn giản tại thư mục gốc của dự án này, file này sẽ chứa mô tả của thư viện, tính năng hỗ trợ, các liên kết tham khảo, cấu trúc cơ bản của dự án, kế hoạch phát triển theo từng bước, file này phải chứa đầy đủ context để các công cụ AI coding assistant có thể đọc và hiểu được rõ và chính xác mục đích của dự án

---

bạn hiểu sai ý tôi rồi, tôi chỉ cần 1 thư viện đơn giản thôi, để tôi giải thích rõ hơn kèm ví dụ cụ thể:
để ý đoạn code này:

```
service ShredstreamProxy {
  rpc SubscribeEntries(SubscribeEntriesRequest) returns (stream Entry);
}

message SubscribeEntriesRequest {
  // tbd: we may want to add filters here
}

message Entry {
  // the slot that the entry is from
  uint64 slot = 1;

  // Serialized bytes of Vec<Entry>: https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html
  bytes entries = 2;
}
```

chú ý vào đoạn

```
message Entry {
  // the slot that the entry is from
  uint64 slot = 1;

  // Serialized bytes of Vec<Entry>: https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html
  bytes entries = 2;
}
```

tôi đã có sẵn 1 client để nhận được `message Entry` này rồi, bây giờ thư viện này tôi chỉ cần 1 hàm duy nhất, với input truyền vào là 1 object dạng `{ slot: bigint, entries: Buffer }`, chúng ta sẽ có return là `{ slot: bigint, entries: Entry[] }`
bạn hiểu ý tôi chứ, nếu hiểu thì cập nhật lại file `README.md` nhé
